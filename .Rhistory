# Load required libraries
library(readxl)
library(vegan)
library(tidyverse)
library(ggplot2)
library(gridExtra)
library(RColorBrewer)
# Set up output directory
if (!dir.exists("statistical_results")) {
dir.create("statistical_results")
}
cat("========== LOADING DATA FILES ==========\n")
# Read data files
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
fungi_otu <- read_excel("OTU_table_fungi.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")
# Print data dimensions
cat("Data dimensions:\n")
cat("Bacteria OTU:", dim(bacteria_otu), "\n")
cat("Fungi OTU:", dim(fungi_otu), "\n")
cat("Bacteria metadata:", dim(bacteria_meta), "\n")
cat("Fungi metadata:", dim(fungi_meta), "\n")
# Check factor levels
cat("\nFactor levels:\n")
cat("Season levels:", unique(bacteria_meta$Season), "\n")
cat("Protection levels:", unique(bacteria_meta$Protection), "\n")
# Function to prepare OTU data for analysis
prepare_otu_data <- function(otu_table, metadata) {
# Get sample ID column name
sample_id_col <- colnames(otu_table)[1]
# Extract abundance matrix (samples as rows, OTUs as columns)
# First column contains OTU IDs, remaining columns are samples
abundance_matrix <- as.matrix(otu_table[, -1])
rownames(abundance_matrix) <- otu_table[[1]]
# Transpose so samples are rows and OTUs are columns
abundance_matrix <- t(abundance_matrix)
# Match samples between OTU table and metadata
common_samples <- intersect(rownames(abundance_matrix), metadata$Sample)
if (length(common_samples) == 0) {
stop("No common samples found between OTU table and metadata")
}
# Filter to common samples
abundance_matrix <- abundance_matrix[common_samples, ]
metadata_filtered <- metadata[metadata$Sample %in% common_samples, ]
# Ensure same order
metadata_filtered <- metadata_filtered[match(rownames(abundance_matrix), metadata_filtered$Sample), ]
cat("Prepared data with", nrow(abundance_matrix), "samples and", ncol(abundance_matrix), "OTUs\n")
return(list(
abundance = abundance_matrix,
metadata = metadata_filtered
))
}
# Function to perform betadisper analysis
perform_betadisper <- function(distance_matrix, grouping_factor, factor_name) {
cat("\n--- Betadisper Analysis for", factor_name, "---\n")
# Perform betadisper
betadisp_result <- betadisper(distance_matrix, grouping_factor)
# Test for homogeneity of dispersions
betadisp_test <- permutest(betadisp_result, pairwise = TRUE, permutations = 999)
cat("Betadisper test results:\n")
cat("F-statistic:", betadisp_test$statistic, "\n")
cat("p-value:", betadisp_test$p.value, "\n")
if (betadisp_test$p.value < 0.05) {
cat("*** Significant difference in dispersions (p < 0.05) ***\n")
} else {
cat("No significant difference in dispersions (p >= 0.05)\n")
}
return(list(
betadisp = betadisp_result,
test = betadisp_test
))
}
# Function to perform PERMANOVA analysis
perform_permanova <- function(distance_matrix, metadata, formula_str) {
cat("\n--- PERMANOVA Analysis ---\n")
cat("Formula:", formula_str, "\n")
# Perform PERMANOVA
permanova_result <- adonis2(as.formula(formula_str), data = metadata, permutations = 999)
cat("PERMANOVA results:\n")
print(permanova_result)
return(permanova_result)
}
# Function to create distance matrix
create_distance_matrix <- function(abundance_matrix, method = "bray") {
cat("Creating", method, "distance matrix...\n")
# Remove OTUs with zero abundance across all samples
abundance_matrix <- abundance_matrix[, colSums(abundance_matrix) > 0]
cat("Using", ncol(abundance_matrix), "OTUs after filtering zeros\n")
# Create distance matrix
dist_matrix <- vegdist(abundance_matrix, method = method)
return(dist_matrix)
}
cat("\n========== PREPARING DATA ==========\n")
# Prepare bacteria data
bacteria_data <- prepare_otu_data(bacteria_otu, bacteria_meta)
bacteria_dist <- create_distance_matrix(bacteria_data$abundance)
# Prepare fungi data
fungi_data <- prepare_otu_data(fungi_otu, fungi_meta)
fungi_dist <- create_distance_matrix(fungi_data$abundance)
cat("\n========== BACTERIA COMMUNITY ANALYSIS ==========\n")
# Bacteria - Overall analysis
cat("\n=== BACTERIA: Overall Season and Protection Analysis ===\n")
# Betadisper for Season
bacteria_season_betadisp <- perform_betadisper(bacteria_dist, bacteria_data$metadata$Season, "Season")
