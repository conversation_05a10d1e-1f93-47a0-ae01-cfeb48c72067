# Betadisper and PERMANOVA Analysis for Bacterial and Fungal Communities
# This script performs statistical analyses to test for significant differences
# in community composition based on Season and Protection factors

# Load required libraries
library(readxl)
library(vegan)
library(tidyverse)
library(ggplot2)
library(gridExtra)
library(RColorBrewer)

# Set up output directory
if (!dir.exists("statistical_results")) {
  dir.create("statistical_results")
}

cat("========== LOADING DATA FILES ==========\n")

# Read data files
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
fungi_otu <- read_excel("OTU_table_fungi.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

# Print data dimensions
cat("Data dimensions:\n")
cat("Bacteria OTU:", dim(bacteria_otu), "\n")
cat("Fungi OTU:", dim(fungi_otu), "\n")
cat("Bacteria metadata:", dim(bacteria_meta), "\n")
cat("Fungi metadata:", dim(fungi_meta), "\n")

# Check factor levels
cat("\nFactor levels:\n")
cat("Season levels:", unique(bacteria_meta$Season), "\n")
cat("Protection levels:", unique(bacteria_meta$Protection), "\n")

# Function to prepare OTU data for analysis
prepare_otu_data <- function(otu_table, metadata) {
  # Get sample ID column name
  sample_id_col <- colnames(otu_table)[1]
  
  # Extract abundance matrix (samples as rows, OTUs as columns)
  # First column contains OTU IDs, remaining columns are samples
  abundance_matrix <- as.matrix(otu_table[, -1])
  rownames(abundance_matrix) <- otu_table[[1]]
  
  # Transpose so samples are rows and OTUs are columns
  abundance_matrix <- t(abundance_matrix)
  
  # Match samples between OTU table and metadata
  common_samples <- intersect(rownames(abundance_matrix), metadata$Sample)
  
  if (length(common_samples) == 0) {
    stop("No common samples found between OTU table and metadata")
  }
  
  # Filter to common samples
  abundance_matrix <- abundance_matrix[common_samples, ]
  metadata_filtered <- metadata[metadata$Sample %in% common_samples, ]
  
  # Ensure same order
  metadata_filtered <- metadata_filtered[match(rownames(abundance_matrix), metadata_filtered$Sample), ]
  
  cat("Prepared data with", nrow(abundance_matrix), "samples and", ncol(abundance_matrix), "OTUs\n")
  
  return(list(
    abundance = abundance_matrix,
    metadata = metadata_filtered
  ))
}

# Function to perform betadisper analysis
perform_betadisper <- function(distance_matrix, grouping_factor, factor_name) {
  cat("\n--- Betadisper Analysis for", factor_name, "---\n")

  # Perform betadisper
  betadisp_result <- betadisper(distance_matrix, grouping_factor)

  # Test for homogeneity of dispersions
  betadisp_test <- permutest(betadisp_result, pairwise = TRUE, permutations = 999)

  cat("Betadisper test results:\n")
  cat("F-statistic:", betadisp_test$statistic, "\n")

  # Debug: print the structure of p.value
  cat("P-value structure:\n")
  print(str(betadisp_test$p.value))
  print(betadisp_test$p.value)

  # Extract the overall p-value more safely
  if (is.null(betadisp_test$p.value)) {
    overall_p <- NA
  } else if (length(betadisp_test$p.value) == 1) {
    overall_p <- betadisp_test$p.value
  } else {
    # If multiple p-values, take the first one (overall test)
    overall_p <- betadisp_test$p.value[1]
  }

  cat("Extracted p-value:", overall_p, "\n")

  if (is.na(overall_p)) {
    cat("Warning: Could not extract p-value from betadisper test\n")
  } else if (overall_p < 0.05) {
    cat("*** Significant difference in dispersions (p < 0.05) ***\n")
  } else {
    cat("No significant difference in dispersions (p >= 0.05)\n")
  }

  return(list(
    betadisp = betadisp_result,
    test = betadisp_test,
    p_value = overall_p
  ))
}

# Function to perform PERMANOVA analysis
perform_permanova <- function(distance_matrix, metadata, formula_str) {
  cat("\n--- PERMANOVA Analysis ---\n")
  cat("Formula:", formula_str, "\n")
  
  # Perform PERMANOVA
  permanova_result <- adonis2(as.formula(formula_str), data = metadata, permutations = 999)
  
  cat("PERMANOVA results:\n")
  print(permanova_result)
  
  return(permanova_result)
}

# Function to create distance matrix
create_distance_matrix <- function(abundance_matrix, method = "bray") {
  cat("Creating", method, "distance matrix...\n")
  
  # Remove OTUs with zero abundance across all samples
  abundance_matrix <- abundance_matrix[, colSums(abundance_matrix) > 0]
  
  cat("Using", ncol(abundance_matrix), "OTUs after filtering zeros\n")
  
  # Create distance matrix
  dist_matrix <- vegdist(abundance_matrix, method = method)
  
  return(dist_matrix)
}

cat("\n========== PREPARING DATA ==========\n")

# Prepare bacteria data
bacteria_data <- prepare_otu_data(bacteria_otu, bacteria_meta)
bacteria_dist <- create_distance_matrix(bacteria_data$abundance)

# Prepare fungi data
fungi_data <- prepare_otu_data(fungi_otu, fungi_meta)
fungi_dist <- create_distance_matrix(fungi_data$abundance)

cat("\n========== BACTERIA COMMUNITY ANALYSIS ==========\n")

# Bacteria - Overall analysis
cat("\n=== BACTERIA: Overall Season and Protection Analysis ===\n")

# Betadisper for Season
bacteria_season_betadisp <- perform_betadisper(bacteria_dist, bacteria_data$metadata$Season, "Season")

# Betadisper for Protection
bacteria_protection_betadisp <- perform_betadisper(bacteria_dist, bacteria_data$metadata$Protection, "Protection")

# PERMANOVA for Season and Protection
bacteria_permanova_full <- perform_permanova(bacteria_dist, bacteria_data$metadata, "bacteria_dist ~ Season + Protection + Season:Protection")

# PERMANOVA for Season only
bacteria_permanova_season <- perform_permanova(bacteria_dist, bacteria_data$metadata, "bacteria_dist ~ Season")

# PERMANOVA for Protection only
bacteria_permanova_protection <- perform_permanova(bacteria_dist, bacteria_data$metadata, "bacteria_dist ~ Protection")

cat("\n=== BACTERIA: Season-specific Protection Analysis ===\n")

# Season-specific analysis for bacteria
bacteria_season_results <- list()
seasons <- unique(bacteria_data$metadata$Season)

for (season in seasons) {
  cat("\n--- Bacteria Analysis for", season, "Season ---\n")
  
  # Filter data for current season
  season_indices <- bacteria_data$metadata$Season == season
  season_metadata <- bacteria_data$metadata[season_indices, ]
  season_abundance <- bacteria_data$abundance[season_indices, ]
  
  # Create distance matrix for season subset
  season_dist <- create_distance_matrix(season_abundance)
  
  # Betadisper for Protection within season
  season_betadisp <- perform_betadisper(season_dist, season_metadata$Protection, paste("Protection in", season))
  
  # PERMANOVA for Protection within season
  season_permanova <- perform_permanova(season_dist, season_metadata, "season_dist ~ Protection")
  
  bacteria_season_results[[season]] <- list(
    betadisp = season_betadisp,
    permanova = season_permanova,
    n_samples = nrow(season_metadata)
  )
}

cat("\n========== FUNGI COMMUNITY ANALYSIS ==========\n")

# Fungi - Overall analysis
cat("\n=== FUNGI: Overall Season and Protection Analysis ===\n")

# Betadisper for Season
fungi_season_betadisp <- perform_betadisper(fungi_dist, fungi_data$metadata$Season, "Season")

# Betadisper for Protection
fungi_protection_betadisp <- perform_betadisper(fungi_dist, fungi_data$metadata$Protection, "Protection")

# PERMANOVA for Season and Protection
fungi_permanova_full <- perform_permanova(fungi_dist, fungi_data$metadata, "fungi_dist ~ Season + Protection + Season:Protection")

# PERMANOVA for Season only
fungi_permanova_season <- perform_permanova(fungi_dist, fungi_data$metadata, "fungi_dist ~ Season")

# PERMANOVA for Protection only
fungi_permanova_protection <- perform_permanova(fungi_dist, fungi_data$metadata, "fungi_dist ~ Protection")

cat("\n=== FUNGI: Season-specific Protection Analysis ===\n")

# Season-specific analysis for fungi
fungi_season_results <- list()

for (season in seasons) {
  cat("\n--- Fungi Analysis for", season, "Season ---\n")

  # Filter data for current season
  season_indices <- fungi_data$metadata$Season == season
  season_metadata <- fungi_data$metadata[season_indices, ]
  season_abundance <- fungi_data$abundance[season_indices, ]

  # Create distance matrix for season subset
  season_dist <- create_distance_matrix(season_abundance)

  # Betadisper for Protection within season
  season_betadisp <- perform_betadisper(season_dist, season_metadata$Protection, paste("Protection in", season))

  # PERMANOVA for Protection within season
  season_permanova <- perform_permanova(season_dist, season_metadata, "season_dist ~ Protection")

  fungi_season_results[[season]] <- list(
    betadisp = season_betadisp,
    permanova = season_permanova,
    n_samples = nrow(season_metadata)
  )
}

cat("\n========== CREATING SUMMARY RESULTS ==========\n")

# Function to extract key results
extract_results <- function(betadisp_result, permanova_result, analysis_name) {
  # Extract betadisper p-value safely
  betadisp_p <- if (!is.null(betadisp_result$p_value)) {
    betadisp_result$p_value
  } else {
    betadisp_result$test$p.value[1]
  }

  data.frame(
    Analysis = analysis_name,
    Betadisper_F = betadisp_result$test$statistic,
    Betadisper_p = betadisp_p,
    Betadisper_Significant = !is.na(betadisp_p) && betadisp_p < 0.05,
    PERMANOVA_F = permanova_result$F[1],
    PERMANOVA_R2 = permanova_result$R2[1],
    PERMANOVA_p = permanova_result$`Pr(>F)`[1],
    PERMANOVA_Significant = !is.na(permanova_result$`Pr(>F)`[1]) && permanova_result$`Pr(>F)`[1] < 0.05,
    stringsAsFactors = FALSE
  )
}

# Create summary table
summary_results <- data.frame()

# Bacteria overall results
summary_results <- rbind(summary_results,
  extract_results(bacteria_season_betadisp, bacteria_permanova_season, "Bacteria_Season"))
summary_results <- rbind(summary_results,
  extract_results(bacteria_protection_betadisp, bacteria_permanova_protection, "Bacteria_Protection"))

# Fungi overall results
summary_results <- rbind(summary_results,
  extract_results(fungi_season_betadisp, fungi_permanova_season, "Fungi_Season"))
summary_results <- rbind(summary_results,
  extract_results(fungi_protection_betadisp, fungi_permanova_protection, "Fungi_Protection"))

# Season-specific results for bacteria
for (season in seasons) {
  result <- bacteria_season_results[[season]]
  summary_results <- rbind(summary_results,
    extract_results(result$betadisp, result$permanova, paste("Bacteria", season, "Protection")))
}

# Season-specific results for fungi
for (season in seasons) {
  result <- fungi_season_results[[season]]
  summary_results <- rbind(summary_results,
    extract_results(result$betadisp, result$permanova, paste("Fungi", season, "Protection")))
}

# Save summary results
write.csv(summary_results, "statistical_results/betadisper_permanova_summary.csv", row.names = FALSE)

cat("\nSummary results saved to: statistical_results/betadisper_permanova_summary.csv\n")

# Print summary
cat("\n========== SUMMARY OF RESULTS ==========\n")
print(summary_results)

cat("\n========== INTERPRETATION GUIDE ==========\n")
cat("Betadisper tests for homogeneity of dispersions (variance):\n")
cat("- Significant result (p < 0.05) indicates unequal dispersions between groups\n")
cat("- Non-significant result (p >= 0.05) indicates equal dispersions\n\n")

cat("PERMANOVA tests for differences in community composition:\n")
cat("- Significant result (p < 0.05) indicates significant differences between groups\n")
cat("- R2 value indicates proportion of variance explained by the factor\n")
cat("- F-statistic indicates the strength of the effect\n\n")

cat("Important: If betadisper is significant, PERMANOVA results should be interpreted with caution\n")
cat("as differences might be due to dispersion rather than location effects.\n")

cat("\n========== CREATING VISUALIZATIONS ==========\n")

# Function to create betadisper plots
create_betadisper_plots <- function(betadisp_result, title, filename) {
  pdf(paste0("statistical_results/", filename), width = 10, height = 8)

  # Plot distances to centroids
  plot(betadisp_result, main = paste("Betadisper:", title))

  # Boxplot of distances
  boxplot(betadisp_result, main = paste("Distance to Centroid:", title))

  dev.off()
  cat("Betadisper plots saved:", filename, "\n")
}

# Function to create NMDS ordination plots
create_ordination_plots <- function(distance_matrix, metadata, title, filename) {
  # Perform NMDS
  nmds_result <- metaMDS(distance_matrix, k = 2, trymax = 100)

  pdf(paste0("statistical_results/", filename), width = 12, height = 10)

  par(mfrow = c(2, 2))

  # Plot by Season
  plot(nmds_result, type = "n", main = paste(title, "- by Season"))
  colors_season <- brewer.pal(length(unique(metadata$Season)), "Set1")
  points(nmds_result, col = colors_season[as.factor(metadata$Season)], pch = 19, cex = 1.2)
  legend("topright", legend = unique(metadata$Season), col = colors_season, pch = 19)

  # Plot by Protection
  plot(nmds_result, type = "n", main = paste(title, "- by Protection"))
  colors_protection <- c("red", "blue")
  points(nmds_result, col = colors_protection[as.factor(metadata$Protection)], pch = 19, cex = 1.2)
  legend("topright", legend = unique(metadata$Protection), col = colors_protection, pch = 19)

  # Plot by Season and Protection combined
  plot(nmds_result, type = "n", main = paste(title, "- Season x Protection"))
  metadata$Combined <- paste(metadata$Season, metadata$Protection, sep = "_")
  colors_combined <- rainbow(length(unique(metadata$Combined)))
  points(nmds_result, col = colors_combined[as.factor(metadata$Combined)], pch = 19, cex = 1.2)
  legend("topright", legend = unique(metadata$Combined), col = colors_combined, pch = 19, cex = 0.8)

  # Stress plot
  stressplot(nmds_result, main = paste("Stress Plot -", title))

  dev.off()
  cat("Ordination plots saved:", filename, "\n")

  return(nmds_result)
}

# Create visualizations for bacteria
create_betadisper_plots(bacteria_season_betadisp$betadisp, "Bacteria Season", "bacteria_season_betadisper.pdf")
create_betadisper_plots(bacteria_protection_betadisp$betadisp, "Bacteria Protection", "bacteria_protection_betadisper.pdf")

bacteria_nmds <- create_ordination_plots(bacteria_dist, bacteria_data$metadata, "Bacteria Communities", "bacteria_ordination.pdf")

# Create visualizations for fungi
create_betadisper_plots(fungi_season_betadisp$betadisp, "Fungi Season", "fungi_season_betadisper.pdf")
create_betadisper_plots(fungi_protection_betadisp$betadisp, "Fungi Protection", "fungi_protection_betadisper.pdf")

fungi_nmds <- create_ordination_plots(fungi_dist, fungi_data$metadata, "Fungi Communities", "fungi_ordination.pdf")

# Create summary visualization
pdf("statistical_results/statistical_summary_plots.pdf", width = 14, height = 10)

par(mfrow = c(2, 3), mar = c(8, 4, 4, 2))

# Plot 1: Betadisper p-values
betadisp_data <- summary_results[, c("Analysis", "Betadisper_p")]
barplot(betadisp_data$Betadisper_p, names.arg = betadisp_data$Analysis,
        main = "Betadisper p-values", ylab = "p-value", las = 2, cex.names = 0.7)
abline(h = 0.05, col = "red", lty = 2)

# Plot 2: PERMANOVA p-values
permanova_data <- summary_results[, c("Analysis", "PERMANOVA_p")]
barplot(permanova_data$PERMANOVA_p, names.arg = permanova_data$Analysis,
        main = "PERMANOVA p-values", ylab = "p-value", las = 2, cex.names = 0.7)
abline(h = 0.05, col = "red", lty = 2)

# Plot 3: PERMANOVA R2 values
r2_data <- summary_results[, c("Analysis", "PERMANOVA_R2")]
barplot(r2_data$PERMANOVA_R2, names.arg = r2_data$Analysis,
        main = "PERMANOVA R² values", ylab = "R²", las = 2, cex.names = 0.7)

# Plot 4: PERMANOVA F-statistics
f_data <- summary_results[, c("Analysis", "PERMANOVA_F")]
barplot(f_data$PERMANOVA_F, names.arg = f_data$Analysis,
        main = "PERMANOVA F-statistics", ylab = "F-statistic", las = 2, cex.names = 0.7)

# Plot 5: Significance summary
sig_summary <- table(summary_results$PERMANOVA_Significant)
pie(sig_summary, main = "PERMANOVA Significance",
    labels = paste(names(sig_summary), "\n(", sig_summary, ")", sep = ""))

# Plot 6: Betadisper significance summary
betadisp_sig_summary <- table(summary_results$Betadisper_Significant)
pie(betadisp_sig_summary, main = "Betadisper Significance",
    labels = paste(names(betadisp_sig_summary), "\n(", betadisp_sig_summary, ")", sep = ""))

dev.off()

cat("Summary plots saved: statistical_results/statistical_summary_plots.pdf\n")

cat("\n========== ANALYSIS COMPLETE ==========\n")
cat("All results have been saved to the 'statistical_results' directory:\n")
cat("- betadisper_permanova_summary.csv: Summary table of all statistical tests\n")
cat("- bacteria_season_betadisper.pdf: Betadisper plots for bacteria by season\n")
cat("- bacteria_protection_betadisper.pdf: Betadisper plots for bacteria by protection\n")
cat("- bacteria_ordination.pdf: NMDS ordination plots for bacteria\n")
cat("- fungi_season_betadisper.pdf: Betadisper plots for fungi by season\n")
cat("- fungi_protection_betadisper.pdf: Betadisper plots for fungi by protection\n")
cat("- fungi_ordination.pdf: NMDS ordination plots for fungi\n")
cat("- statistical_summary_plots.pdf: Summary visualization of all results\n")
