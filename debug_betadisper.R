# Debug script to understand betadisper p-value structure
library(readxl)
library(vegan)

# Load a small subset of data for testing
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")

# Prepare data
abundance_matrix <- as.matrix(bacteria_otu[, -1])
rownames(abundance_matrix) <- bacteria_otu[[1]]
abundance_matrix <- t(abundance_matrix)

common_samples <- intersect(rownames(abundance_matrix), bacteria_meta$Sample)
abundance_matrix <- abundance_matrix[common_samples, ]
metadata_filtered <- bacteria_meta[bacteria_meta$Sample %in% common_samples, ]
metadata_filtered <- metadata_filtered[match(rownames(abundance_matrix), metadata_filtered$Sample), ]

# Remove zero-abundance OTUs
abundance_matrix <- abundance_matrix[, colSums(abundance_matrix) > 0]

# Create distance matrix
dist_matrix <- vegdist(abundance_matrix, method = "bray")

cat("========== DEBUGGING BETADISPER ==========\n")

# Test betadisper with Protection (2 groups)
cat("\n--- Testing betadisper with Protection ---\n")
betadisp_protection <- betadisper(dist_matrix, metadata_filtered$Protection)
betadisp_test_protection <- permutest(betadisp_protection, permutations = 99)

cat("Betadisper object structure:\n")
print(str(betadisp_test_protection))

cat("\nBetadisper test object:\n")
print(betadisp_test_protection)

cat("\nP-value component:\n")
print(betadisp_test_protection$p.value)
cat("Class of p.value:", class(betadisp_test_protection$p.value), "\n")
cat("Length of p.value:", length(betadisp_test_protection$p.value), "\n")

cat("\nTrying different extraction methods:\n")
cat("Method 1 - Direct:", betadisp_test_protection$p.value, "\n")
cat("Method 2 - First element:", betadisp_test_protection$p.value[1], "\n")

# Check if it's in the tab component
if ("tab" %in% names(betadisp_test_protection)) {
  cat("Tab component exists:\n")
  print(betadisp_test_protection$tab)
  if ("Pr(>F)" %in% colnames(betadisp_test_protection$tab)) {
    cat("P-value from tab:", betadisp_test_protection$tab[1, "Pr(>F)"], "\n")
  }
}

# Alternative approach - use anova on betadisper object
cat("\n--- Testing anova approach ---\n")
anova_result <- anova(betadisp_protection)
cat("Anova on betadisper:\n")
print(anova_result)

if ("Pr(>F)" %in% colnames(anova_result)) {
  cat("P-value from anova:", anova_result[1, "Pr(>F)"], "\n")
}

cat("\n========== TESTING WITH SEASON (3 groups) ==========\n")

# Test with Season (3 groups)
betadisp_season <- betadisper(dist_matrix, metadata_filtered$Season)
betadisp_test_season <- permutest(betadisp_season, permutations = 99)

cat("Season betadisper p-value:\n")
print(betadisp_test_season$p.value)

# Try anova approach for season
anova_season <- anova(betadisp_season)
cat("Season anova on betadisper:\n")
print(anova_season)

cat("\n========== SUMMARY ==========\n")
cat("This will help us understand the correct way to extract p-values\n")
