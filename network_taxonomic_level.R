# Network Analysis at Taxonomic Level for Fungi and Bacteria by Protection Level
# This script performs network analysis on fungi and bacteria data separately
# filtering samples by Protection level (Low vs High) and creating cross-kingdom networks

# Load required libraries
library(readxl)
library(igraph)
library(vegan)
library(tidyverse)

# Configuration: Choose taxonomic level for analysis
TAXONOMIC_LEVEL <- "Family"  # Options: "Genus", "Family", "Order", "Class"
cat("Performing network analysis at", TAXONOMIC_LEVEL, "level\n")
cat("This provides optimal balance between computational efficiency and biological resolution\n")


# Read data files
cat("========== LOADING DATA FILES ==========\n")

# OTU tables
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
fungi_otu <- read_excel("OTU_table_fungi.xlsx")

# Taxonomy files
bacteria_tax <- read_excel("taxonomy_bacteria.xlsx")
fungi_tax <- read_excel("taxonomy_fungi.xlsx")

# Metadata files
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

# Print data dimensions for debugging
cat("Original data dimensions:\n")
cat("Bacteria OTU:", dim(bacteria_otu), "\n")
cat("Fungi OTU:", dim(fungi_otu), "\n")
cat("Bacteria metadata:", dim(bacteria_meta), "\n")
cat("Fungi metadata:", dim(fungi_meta), "\n")

# Check Protection levels
cat("Protection levels in bacteria metadata:", unique(bacteria_meta$Protection), "\n")
cat("Protection levels in fungi metadata:", unique(fungi_meta$Protection), "\n")

# Function to aggregate OTUs to specified taxonomic level
aggregate_to_taxonomic_level <- function(otu_table, taxonomy_table, taxonomic_level) {
  cat("\n--- Aggregating to", taxonomic_level, "level ---\n")
  
  # Check if taxonomic level exists in taxonomy table
  if (!taxonomic_level %in% colnames(taxonomy_table)) {
    stop("Taxonomic level '", taxonomic_level, "' not found in taxonomy table")
  }
  
  # Get the first column name (OTU ID column)
  otu_id_col <- colnames(otu_table)[1]
  tax_otu_id_col <- colnames(taxonomy_table)[1]
  
  # Merge OTU table with taxonomy
  merged_data <- merge(otu_table, taxonomy_table[, c(tax_otu_id_col, taxonomic_level)], 
                       by.x = otu_id_col, by.y = tax_otu_id_col, all.x = TRUE)
  
  # Remove rows with NA or empty taxonomic assignment
  valid_rows <- !is.na(merged_data[[taxonomic_level]]) & 
                merged_data[[taxonomic_level]] != "" & 
                merged_data[[taxonomic_level]] != "NA"
  
  if (sum(valid_rows) == 0) {
    stop("No valid taxonomic assignments found at ", taxonomic_level, " level")
  }
  
  cat("Removed", sum(!valid_rows), "OTUs with missing", taxonomic_level, "assignment\n")
  merged_data <- merged_data[valid_rows, ]
  
  # Get sample columns (all columns except OTU ID and taxonomic level)
  sample_cols <- setdiff(colnames(merged_data), c(otu_id_col, taxonomic_level))
  
  # Aggregate by summing OTU counts within each taxonomic group
  aggregated <- merged_data %>%
    group_by(!!sym(taxonomic_level)) %>%
    summarise(across(all_of(sample_cols), sum, na.rm = TRUE), .groups = 'drop')
  
  # Rename the taxonomic level column to match original OTU ID column name
  colnames(aggregated)[1] <- otu_id_col
  
  cat("Aggregated from", nrow(otu_table), "OTUs to", nrow(aggregated), taxonomic_level, "groups\n")
  
  return(as.data.frame(aggregated))
}

# Function to filter samples by protection level (Low vs High)
filter_samples_by_protection <- function(otu_table, metadata, protection_level) {
  # Determine the sample ID column name (different files use different names)
  sample_id_col <- NULL
  if ("Sample_ID" %in% colnames(metadata)) {
    sample_id_col <- "Sample_ID"
  } else if ("Sample" %in% colnames(metadata)) {
    sample_id_col <- "Sample"
  } else if ("sample" %in% colnames(metadata)) {
    sample_id_col <- "sample"
  } else {
    cat("Error: Could not find sample ID column in metadata\n")
    return(NULL)
  }

  # Get sample IDs for the specified protection level
  target_samples <- metadata[[sample_id_col]][metadata$Protection == protection_level]

  cat("Found", length(target_samples), "target samples for protection level", protection_level, "\n")

  # Find which columns in OTU table correspond to these samples
  sample_cols <- which(colnames(otu_table) %in% target_samples)

  if (length(sample_cols) == 0) {
    cat("Warning: No samples found for protection level", protection_level, "\n")
    return(NULL)
  }

  # Return OTU table with only the target samples (plus the first column with IDs)
  filtered_table <- otu_table[, c(1, sample_cols)]
  cat("Filtered", protection_level, "protection samples:", ncol(filtered_table) - 1, "samples\n")
  return(filtered_table)
}

# Aggregate datasets to the specified taxonomic level
cat("\n========== AGGREGATING TO TAXONOMIC LEVEL ==========\n")

bacteria_agg <- aggregate_to_taxonomic_level(bacteria_otu, bacteria_tax, TAXONOMIC_LEVEL)
fungi_agg <- aggregate_to_taxonomic_level(fungi_otu, fungi_tax, TAXONOMIC_LEVEL)

cat("\nAggregated data dimensions:\n")
cat("Bacteria:", dim(bacteria_agg), "\n")
cat("Fungi:", dim(fungi_agg), "\n")

# Create filtered datasets for Low and High protection levels
cat("\n========== FILTERING SAMPLES BY PROTECTION LEVEL ==========\n")

# Bacteria by protection level
bacteria_low <- filter_samples_by_protection(bacteria_agg, bacteria_meta, "Low")
bacteria_high <- filter_samples_by_protection(bacteria_agg, bacteria_meta, "High")

# Fungi by protection level
fungi_low <- filter_samples_by_protection(fungi_agg, fungi_meta, "Low")
fungi_high <- filter_samples_by_protection(fungi_agg, fungi_meta, "High")

cat("\n========== SUMMARY OF FILTERED DATASETS ==========\n")
if (!is.null(bacteria_low)) cat("Bacteria Low Protection:", dim(bacteria_low), "\n")
if (!is.null(bacteria_high)) cat("Bacteria High Protection:", dim(bacteria_high), "\n")
if (!is.null(fungi_low)) cat("Fungi Low Protection:", dim(fungi_low), "\n")
if (!is.null(fungi_high)) cat("Fungi High Protection:", dim(fungi_high), "\n")

cat("\nData aggregation and filtering completed successfully!\n")

# Function to create correlation matrix for taxonomic-level data
create_taxonomic_correlation_matrix <- function(taxonomic_table, threshold = 0.6) {
  cat("\n--- Creating correlation matrix ---\n")
  cat("Input dimensions:", dim(taxonomic_table), "\n")

  # Check if the table has at least one column besides the taxonomic IDs
  if (ncol(taxonomic_table) <= 1) {
    stop("Table must have at least one sample column besides the taxonomic IDs")
  }

  tryCatch({
    # Extract abundance matrix (remove first column which contains taxonomic names)
    abundance_matrix <- as.matrix(taxonomic_table[,-1])
    rownames(abundance_matrix) <- taxonomic_table[[1]]

    # Check for rows with all zeros or constant values
    row_sums <- rowSums(abundance_matrix)
    constant_rows <- row_sums == 0 | apply(abundance_matrix, 1, function(x) length(unique(x)) <= 1)

    if (any(constant_rows)) {
      cat("Removing", sum(constant_rows), "constant rows (all zeros or all same value)\n")
      abundance_matrix <- abundance_matrix[!constant_rows, , drop = FALSE]
    }

    # Check if we still have enough rows
    if (nrow(abundance_matrix) <= 1) {
      stop("Not enough variable rows for correlation analysis after removing constant rows")
    }

    cat("Final matrix for correlation:", nrow(abundance_matrix), "taxa ×", ncol(abundance_matrix), "samples\n")

    # Calculate correlations
    cat("Calculating Spearman correlations...\n")
    cor_matrix <- cor(t(abundance_matrix), method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cor_matrix))) {
      cat("Replacing", sum(is.na(cor_matrix)), "NA values with 0\n")
      cor_matrix[is.na(cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cor_matrix), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")
    cat("Range:", range(cor_matrix), "\n")

    # Store signs before thresholding
    signs <- sign(cor_matrix)

    # Apply threshold to absolute values
    cor_matrix[abs(cor_matrix) < threshold] <- 0

    # Restore signs
    cor_matrix <- cor_matrix * signs

    # Set diagonal to zero
    diag(cor_matrix) <- 0

    # Print summary after thresholding
    cat("\nAfter thresholding (|correlation| ≥", threshold, "):\n")
    cat("Total non-zero correlations:", sum(cor_matrix != 0), "\n")
    cat("Positive correlations:", sum(cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cor_matrix < 0), "\n")

    if (sum(cor_matrix != 0) > 0) {
      cat("Range of non-zero correlations:", range(cor_matrix[cor_matrix != 0]), "\n")
    } else {
      cat("No correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cor_matrix,
      taxa_names = rownames(abundance_matrix),
      n_taxa = nrow(abundance_matrix)
    ))

  }, error = function(e) {
    cat("Error in correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze taxonomic network
analyze_taxonomic_network <- function(cor_result, group_name) {
  if (is.null(cor_result)) {
    cat("Cannot analyze network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group_name, "NETWORK ==========\n")

  cor_matrix <- cor_result$cor_matrix
  taxa_names <- cor_result$taxa_names

  # Check if we have any correlations
  if (sum(cor_matrix != 0) == 0) {
    cat("No correlations above threshold. Cannot create network.\n")
    return(NULL)
  }

  tryCatch({
    # Create igraph network from correlation matrix
    network <- graph_from_adjacency_matrix(
      abs(cor_matrix),
      mode = "undirected",
      weighted = TRUE,
      diag = FALSE
    )

    # Add taxa names as vertex names
    V(network)$name <- taxa_names

    # Get edge list to properly assign edge attributes
    edge_list <- as_edgelist(network, names = FALSE)

    # Extract correlation values for each edge
    edge_correlations <- numeric(nrow(edge_list))
    edge_signs <- character(nrow(edge_list))

    for (i in 1:nrow(edge_list)) {
      row_idx <- edge_list[i, 1]
      col_idx <- edge_list[i, 2]
      correlation_value <- cor_matrix[row_idx, col_idx]
      edge_correlations[i] <- correlation_value
      edge_signs[i] <- ifelse(correlation_value > 0, "positive", "negative")
    }

    # Add edge attributes
    E(network)$sign <- edge_signs
    E(network)$correlation <- edge_correlations

    # Calculate network metrics
    cat("Network created with", vcount(network), "nodes and", ecount(network), "edges\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      taxa_names = taxa_names,
      group_name = group_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform individual network analyses for each dataset
cat("\n\n========== INDIVIDUAL NETWORK ANALYSES ==========\n")

# Bacteria Low Protection
if (!is.null(bacteria_low)) {
  bacteria_low_cor <- create_taxonomic_correlation_matrix(bacteria_low, threshold = 0.6)
  bacteria_low_network <- analyze_taxonomic_network(bacteria_low_cor, "Bacteria_Low_Protection")
}

# Bacteria High Protection
if (!is.null(bacteria_high)) {
  bacteria_high_cor <- create_taxonomic_correlation_matrix(bacteria_high, threshold = 0.6)
  bacteria_high_network <- analyze_taxonomic_network(bacteria_high_cor, "Bacteria_High_Protection")
}

# Fungi Low Protection
if (!is.null(fungi_low)) {
  fungi_low_cor <- create_taxonomic_correlation_matrix(fungi_low, threshold = 0.6)
  fungi_low_network <- analyze_taxonomic_network(fungi_low_cor, "Fungi_Low_Protection")
}

# Fungi High Protection
if (!is.null(fungi_high)) {
  fungi_high_cor <- create_taxonomic_correlation_matrix(fungi_high, threshold = 0.6)
  fungi_high_network <- analyze_taxonomic_network(fungi_high_cor, "Fungi_High_Protection")
}

# Function to create cross-correlation matrix between two taxonomic tables
create_taxonomic_cross_correlation_matrix <- function(table1, table2, threshold = 0.6) {
  cat("\n--- Creating cross-correlation matrix ---\n")
  cat("Table 1 dimensions:", dim(table1), "\n")
  cat("Table 2 dimensions:", dim(table2), "\n")

  tryCatch({
    # Extract abundance matrices (remove first column which contains taxonomic names)
    abundance_matrix1 <- as.matrix(table1[,-1])
    abundance_matrix2 <- as.matrix(table2[,-1])

    rownames(abundance_matrix1) <- table1[[1]]
    rownames(abundance_matrix2) <- table2[[1]]

    # Remove constant rows from both matrices
    row_sums1 <- rowSums(abundance_matrix1)
    constant_rows1 <- row_sums1 == 0 | apply(abundance_matrix1, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows1)) {
      cat("Removing", sum(constant_rows1), "constant rows from table 1\n")
      abundance_matrix1 <- abundance_matrix1[!constant_rows1, , drop = FALSE]
    }

    row_sums2 <- rowSums(abundance_matrix2)
    constant_rows2 <- row_sums2 == 0 | apply(abundance_matrix2, 1, function(x) length(unique(x)) <= 1)
    if (any(constant_rows2)) {
      cat("Removing", sum(constant_rows2), "constant rows from table 2\n")
      abundance_matrix2 <- abundance_matrix2[!constant_rows2, , drop = FALSE]
    }

    # Check if we have enough data
    if (nrow(abundance_matrix1) <= 1 || nrow(abundance_matrix2) <= 1) {
      stop("Not enough variable rows for cross-correlation analysis")
    }

    # Get sample names and find common samples
    samples1 <- colnames(abundance_matrix1)
    samples2 <- colnames(abundance_matrix2)
    common_samples <- intersect(samples1, samples2)

    if (length(common_samples) == 0) {
      stop("No common samples found between the two tables")
    }

    cat("Found", length(common_samples), "common samples\n")

    # Subset to common samples
    abundance_matrix1 <- abundance_matrix1[, common_samples, drop = FALSE]
    abundance_matrix2 <- abundance_matrix2[, common_samples, drop = FALSE]

    cat("Final matrices: ", nrow(abundance_matrix1), "×", ncol(abundance_matrix1),
        " vs ", nrow(abundance_matrix2), "×", ncol(abundance_matrix2), "\n")

    # Calculate cross-correlations
    cat("Calculating cross-correlations...\n")
    cross_cor_matrix <- cor(t(abundance_matrix1), t(abundance_matrix2),
                           method = "spearman", use = "pairwise.complete.obs")

    # Replace any NAs with 0
    if (any(is.na(cross_cor_matrix))) {
      cat("Replacing", sum(is.na(cross_cor_matrix)), "NA values with 0\n")
      cross_cor_matrix[is.na(cross_cor_matrix)] <- 0
    }

    # Print summary before thresholding
    cat("\nBefore thresholding:\n")
    cat("Total correlations:", length(cross_cor_matrix), "\n")
    cat("Positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations:", sum(cross_cor_matrix < 0), "\n")
    cat("Range:", range(cross_cor_matrix), "\n")

    # Apply threshold and keep only positive correlations (like individual networks)
    cross_cor_matrix[abs(cross_cor_matrix) < threshold] <- 0
    cross_cor_matrix[cross_cor_matrix < 0] <- 0  # Remove negative correlations

    # Print summary after thresholding
    cat("\nAfter thresholding (correlation ≥", threshold, ") and keeping only positive:\n")
    cat("Total positive correlations:", sum(cross_cor_matrix > 0), "\n")
    cat("Negative correlations removed for consistency with individual networks\n")

    if (sum(cross_cor_matrix > 0) > 0) {
      cat("Range of positive correlations:", range(cross_cor_matrix[cross_cor_matrix > 0]), "\n")
    } else {
      cat("No positive correlations above threshold.\n")
    }

    return(list(
      cor_matrix = cross_cor_matrix,
      taxa_names1 = rownames(abundance_matrix1),
      taxa_names2 = rownames(abundance_matrix2),
      common_samples = common_samples
    ))

  }, error = function(e) {
    cat("Error in cross-correlation calculation:", e$message, "\n")
    return(NULL)
  })
}

# Function to analyze cross-correlation network
analyze_taxonomic_cross_network <- function(cross_cor_result, group1_name, group2_name) {
  if (is.null(cross_cor_result)) {
    cat("Cannot analyze cross-network: correlation result is NULL\n")
    return(NULL)
  }

  cat("\n========== ANALYZING", group1_name, "vs", group2_name, "CROSS-NETWORK ==========\n")

  cross_cor_matrix <- cross_cor_result$cor_matrix
  taxa_names1 <- cross_cor_result$taxa_names1
  taxa_names2 <- cross_cor_result$taxa_names2

  # Check if we have any positive correlations
  if (sum(cross_cor_matrix > 0) == 0) {
    cat("No positive correlations above threshold. Cannot create cross-network.\n")
    return(NULL)
  }

  tryCatch({
    # Create bipartite network with only positive correlations
    # First, create an edge list from the correlation matrix (only positive values)
    edges <- which(cross_cor_matrix > 0, arr.ind = TRUE)
    edge_list <- data.frame(
      from = taxa_names1[edges[,1]],
      to = taxa_names2[edges[,2]],
      weight = cross_cor_matrix[edges],  # All positive, so no need for abs()
      correlation = cross_cor_matrix[edges],
      sign = "positive",  # All edges are positive now
      stringsAsFactors = FALSE
    )

    # Create igraph network
    network <- graph_from_data_frame(edge_list, directed = FALSE)

    # Debug information
    cat("Created network with", vcount(network), "vertices and", ecount(network), "edges\n")
    cat("Vertex names sample:", head(V(network)$name, 10), "\n")

    # Add vertex attributes to distinguish between the two groups
    V(network)$type <- ifelse(V(network)$name %in% taxa_names1, group1_name, group2_name)

    # Verify vertex types
    cat("Group assignments - ", group1_name, ":", sum(V(network)$type == group1_name),
        ", ", group2_name, ":", sum(V(network)$type == group2_name), "\n")

    # Calculate network metrics
    cat("Cross-network created with", vcount(network), "nodes and", ecount(network), "positive edges\n")
    cat("Group 1 (", group1_name, ") nodes:", sum(V(network)$type == group1_name), "\n")
    cat("Group 2 (", group2_name, ") nodes:", sum(V(network)$type == group2_name), "\n")
    cat("Note: Only positive correlations included for consistency with individual networks\n")

    # Node degree
    node_degrees <- degree(network)
    cat("Average degree:", mean(node_degrees), "\n")
    cat("Max degree:", max(node_degrees), "\n")

    # Centrality measures
    betweenness <- betweenness(network)
    closeness <- closeness(network)

    # Community detection
    communities <- cluster_louvain(network)
    community_count <- length(communities)
    modularity_score <- modularity(communities)

    cat("Communities detected:", community_count, "\n")
    cat("Modularity:", modularity_score, "\n")

    # Identify hub nodes (top 5% by degree for more selectivity)
    degree_threshold <- quantile(node_degrees, 0.95)
    hub_nodes <- names(node_degrees[node_degrees >= degree_threshold])
    cat("Hub nodes (top 5% by degree):", length(hub_nodes), "\n")

    # Create results list
    results <- list(
      network = network,
      node_degrees = node_degrees,
      betweenness = betweenness,
      closeness = closeness,
      communities = communities,
      community_count = community_count,
      modularity = modularity_score,
      hub_nodes = hub_nodes,
      edge_list = edge_list,
      group1_name = group1_name,
      group2_name = group2_name
    )

    return(results)

  }, error = function(e) {
    cat("Error in cross-network analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform cross-correlation analyses
cat("\n\n========== CROSS-CORRELATION ANALYSES ==========\n")

# Helper function to perform cross-correlation with multiple thresholds
perform_taxonomic_cross_correlation <- function(table1, table2, group1_name, group2_name) {
  cat("\n\n----- ", group1_name, " vs ", group2_name, " -----\n", sep="")

  # For cross-correlations, start with lower threshold (0.4) since cross-kingdom correlations are typically weaker
  tryCatch({
    cross_cor <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.4)

    if (!is.null(cross_cor) && sum(cross_cor$cor_matrix > 0) > 0) {
      cat("\nFound positive correlations above threshold 0.4\n")
      cross_network <- analyze_taxonomic_cross_network(cross_cor, group1_name, group2_name)
      return(cross_network)
    } else {
      cat("\nNo positive correlations above threshold 0.4. Trying threshold 0.3...\n")

      # Try with threshold 0.3
      cross_cor_03 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.3)

      if (!is.null(cross_cor_03) && sum(cross_cor_03$cor_matrix > 0) > 0) {
        cat("\nFound positive correlations above threshold 0.3\n")
        cross_network <- analyze_taxonomic_cross_network(cross_cor_03,
                                                        paste(group1_name, "(0.3)"),
                                                        paste(group2_name, "(0.3)"))
        return(cross_network)
      } else {
        cat("\nNo positive correlations above threshold 0.3. Trying threshold 0.25...\n")

        # Try with threshold 0.25
        cross_cor_025 <- create_taxonomic_cross_correlation_matrix(table1, table2, threshold = 0.25)

        if (!is.null(cross_cor_025) && sum(cross_cor_025$cor_matrix > 0) > 0) {
          cat("\nFound positive correlations above threshold 0.25\n")
          cross_network <- analyze_taxonomic_cross_network(cross_cor_025,
                                                          paste(group1_name, "(0.25)"),
                                                          paste(group2_name, "(0.25)"))
          return(cross_network)
        } else {
          cat("\nNo significant positive cross-correlations found even at threshold 0.25\n")
          cat("This may indicate weak positive bacteria-fungi interactions in this environment\n")
          return(NULL)
        }
      }
    }
  }, error = function(e) {
    cat("Error in cross-correlation analysis:", e$message, "\n")
    return(NULL)
  })
}

# Perform the 2 cross-correlations for bacteria vs fungi in each protection level

# 1. Bacteria Low Protection vs Fungi Low Protection
if (!is.null(bacteria_low) && !is.null(fungi_low)) {
  cat("Attempting cross-correlation: Bacteria Low Protection vs Fungi Low Protection\n")
  bacteria_fungi_low_network <- perform_taxonomic_cross_correlation(
    bacteria_low, fungi_low,
    "Bacteria_Low_Protection", "Fungi_Low_Protection"
  )
  if (!is.null(bacteria_fungi_low_network)) {
    cat("SUCCESS: Bacteria Low Protection vs Fungi Low Protection cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria Low Protection vs Fungi Low Protection\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria Low Protection vs Fungi Low Protection\n")
}

# 2. Bacteria High Protection vs Fungi High Protection
if (!is.null(bacteria_high) && !is.null(fungi_high)) {
  cat("Attempting cross-correlation: Bacteria High Protection vs Fungi High Protection\n")
  bacteria_fungi_high_network <- perform_taxonomic_cross_correlation(
    bacteria_high, fungi_high,
    "Bacteria_High_Protection", "Fungi_High_Protection"
  )
  if (!is.null(bacteria_fungi_high_network)) {
    cat("SUCCESS: Bacteria High Protection vs Fungi High Protection cross-network created\n")
  } else {
    cat("FAILED: No cross-network created for Bacteria High Protection vs Fungi High Protection\n")
  }
} else {
  cat("SKIPPED: Missing data for Bacteria High Protection vs Fungi High Protection\n")
}

# Function to plot individual networks with improved visualization
plot_individual_network <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    cat("Cannot plot network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- network_result$network

    # Set up layout with better spacing
    layout <- layout_with_fr(network, niter = 1000)

    # Color nodes by community with muted, transparent colors (as you preferred)
    if (network_result$community_count <= 12) {
      # Use a predefined set of muted colors for small numbers of communities
      muted_colors <- c("#8DD3C7", "#FFFFB3", "#BEBADA", "#FB8072", "#80B1D3", "#FDB462",
                       "#B3DE69", "#FCCDE5", "#D9D9D9", "#BC80BD", "#CCEBC5", "#FFED6F")
      community_colors <- muted_colors[1:network_result$community_count]
    } else {
      # For larger numbers, use a muted color palette
      community_colors <- rainbow(network_result$community_count, s = 0.6, v = 0.8)
    }
    # Add transparency to node colors (80% opacity) as you preferred
    node_colors <- adjustcolor(community_colors[membership(network_result$communities)], alpha.f = 0.8)

    # Scale down node sizes significantly and make them more proportional
    max_degree <- max(network_result$node_degrees)
    min_degree <- min(network_result$node_degrees)
    # Scale nodes between 2 and 8 (much smaller than before)
    node_sizes <- 2 + (network_result$node_degrees - min_degree) / (max_degree - min_degree) * 6

    # Improve edge visualization - solid colors with better contrast
    base_edge_colors <- ifelse(E(network)$sign == "positive", "#2E8B57", "#DC143C")  # Sea green and crimson
    edge_colors <- base_edge_colors  # No transparency for better visibility

    # Scale edge widths better
    edge_widths <- 0.5 + abs(E(network)$correlation) * 2

    # More selective hub node identification (top 5% instead of 10%)
    hub_threshold <- quantile(network_result$node_degrees, 0.95)
    is_hub <- network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group_name, "_", taxonomic_level, "_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 14, height = 12)

    # Create layout for main plot and subplot
    layout(matrix(c(1, 1, 1, 2), nrow = 2, ncol = 2, byrow = TRUE), heights = c(3, 1))

    # Main network plot (no labels for cleaner visualization)
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "white",
         vertex.frame.width = 1.0,
         vertex.label = NA,  # Remove all labels for cleaner plot
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.1,
         main = paste(group_name, "Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.2,
         cex.sub = 0.9)

    # Create subplot showing hub families with degree (bars) and betweenness (lines)
    par(mar = c(4, 4, 2, 4))  # Extra margin for second y-axis

    if (sum(is_hub) > 0) {
      hub_families <- V(network)$name[is_hub]
      hub_communities <- membership(network_result$communities)[is_hub]
      hub_degrees <- network_result$node_degrees[is_hub]
      hub_betweenness <- network_result$betweenness[is_hub]

      # Create a summary table
      hub_data <- data.frame(
        Family = hub_families,
        Community = hub_communities,
        Degree = hub_degrees,
        Betweenness = hub_betweenness,
        stringsAsFactors = FALSE
      )

      # Sort by degree (descending)
      hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

      # Limit families to avoid overcrowding
      if (nrow(hub_data) <= 12) {
        families_to_show <- hub_data
      } else {
        families_to_show <- hub_data[1:12, ]
      }

      # Create color mapping for communities
      community_colors_hub <- community_colors[families_to_show$Community]

      # Create bar plot for degree
      bp <- barplot(families_to_show$Degree,
                    names.arg = families_to_show$Family,
                    col = adjustcolor(community_colors_hub, alpha.f = 0.8),
                    las = 2,
                    cex.names = 0.6,
                    main = "Hub Families: Degree (bars) & Betweenness (line)",
                    ylab = "Degree",
                    cex.main = 0.9,
                    ylim = c(0, max(families_to_show$Degree) * 1.1))

      # Add betweenness as a line plot on secondary y-axis
      par(new = TRUE)

      # Scale betweenness to fit nicely with degree scale
      betweenness_scaled <- families_to_show$Betweenness
      if (max(betweenness_scaled) > 0) {
        betweenness_scaled <- (betweenness_scaled / max(betweenness_scaled)) * max(families_to_show$Degree) * 0.8
      }

      plot(bp, betweenness_scaled,
           type = "o",
           pch = 16,
           col = "red",
           lwd = 2,
           cex = 1.2,
           axes = FALSE,
           xlab = "",
           ylab = "")

      # Add right y-axis for betweenness
      axis(4,
           at = seq(0, max(families_to_show$Degree) * 0.8, length.out = 5),
           labels = round(seq(0, max(families_to_show$Betweenness), length.out = 5), 1),
           col = "red",
           col.axis = "red")
      mtext("Betweenness", side = 4, line = 2.5, col = "red", cex = 0.8)

      # Add legend
      legend("topright",
             legend = c("Degree (bars)", "Betweenness (line)"),
             fill = c("gray", NA),
             border = c("black", NA),
             lty = c(NA, 1),
             pch = c(NA, 16),
             col = c("black", "red"),
             cex = 0.7,
             bg = "white")

    } else {
      # If no hub nodes, show a message
      plot.new()
      text(0.5, 0.5, "No hub nodes identified\n(top 5% threshold)",
           cex = 1.2, adj = 0.5)
    }

    # Add improved legend with better positioning
    legend("bottomright",
           legend = c("Positive correlation", "Negative correlation", "Hub nodes (top 5%)", "Communities"),
           col = c("#2E8B57", "#DC143C", "black", "gray"),
           lty = c(1, 1, NA, NA),
           pch = c(NA, NA, 16, 15),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    # Reset layout
    layout(1)

    dev.off()

    cat("Network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")

  }, error = function(e) {
    cat("Error creating network plot:", e$message, "\n")
  })
}

# Function to plot cross-correlation networks with improved visualization
plot_cross_network <- function(cross_network_result, group1_name, group2_name, taxonomic_level) {
  if (is.null(cross_network_result)) {
    cat("Cannot plot cross-network: network result is NULL\n")
    return(NULL)
  }

  tryCatch({
    network <- cross_network_result$network

    # Check if network has vertices
    if (vcount(network) == 0) {
      cat("Network has no vertices, cannot plot\n")
      return(NULL)
    }

    # Try different layouts for better visualization
    # First try bipartite layout, if it fails use force-directed
    layout <- tryCatch({
      layout_as_bipartite(network)
    }, error = function(e) {
      cat("Bipartite layout failed, using force-directed layout\n")
      layout_with_fr(network, niter = 1000)
    })

    # If layout is still problematic, use a simple layout
    if (is.null(layout) || any(is.na(layout))) {
      cat("Using circle layout as fallback\n")
      layout <- layout_in_circle(network)
    }

    # Color nodes by group with muted, transparent colors (as you preferred)
    base_group1_color <- "#87CEEB"  # Muted sky blue for bacteria
    base_group2_color <- "#DDA0DD"  # Muted plum/lavender for fungi (distinct from green edges)
    base_node_colors <- ifelse(V(network)$type == cross_network_result$group1_name, base_group1_color, base_group2_color)
    node_colors <- adjustcolor(base_node_colors, alpha.f = 0.8)  # 80% opacity

    # Scale down node sizes significantly
    max_degree <- max(cross_network_result$node_degrees)
    min_degree <- min(cross_network_result$node_degrees)
    # Scale nodes between 3 and 10 (smaller than before)
    node_sizes <- 3 + (cross_network_result$node_degrees - min_degree) / (max_degree - min_degree) * 7

    # Edge visualization - only positive correlations (green)
    edge_colors <- "#228B22"  # Forest green for all positive correlations

    # Scale edge widths (all correlations are positive now)
    edge_widths <- 0.8 + E(network)$correlation * 2.5

    # More selective hub node identification (top 5%)
    hub_threshold <- quantile(cross_network_result$node_degrees, 0.95)
    is_hub <- cross_network_result$node_degrees >= hub_threshold

    # Create filename
    filename <- paste0(group1_name, "_vs_", group2_name, "_", taxonomic_level, "_cross_network.pdf")

    # Create plot with higher resolution
    pdf(filename, width = 16, height = 12)

    # Create layout for main plot and subplot
    layout(matrix(c(1, 1, 1, 2), nrow = 2, ncol = 2, byrow = TRUE), heights = c(3, 1))

    # Main cross-network plot (no labels for cleaner visualization)
    plot(network,
         layout = layout,
         vertex.color = node_colors,
         vertex.size = node_sizes,
         vertex.frame.color = "white",
         vertex.frame.width = 1.5,
         vertex.label = NA,  # Remove all labels for cleaner plot
         edge.color = edge_colors,
         edge.width = edge_widths,
         edge.curved = 0.15,
         main = paste(group1_name, "vs", group2_name, "Cross-Network at", taxonomic_level, "Level"),
         sub = paste("Nodes:", vcount(network), "| Edges:", ecount(network),
                    "| Communities:", cross_network_result$community_count,
                    "| Hub nodes (top 5%):", sum(is_hub)),
         cex.main = 1.3,
         cex.sub = 0.9)

    # Create subplot showing hub families with degree (bars) and betweenness (lines) by group
    par(mar = c(4, 4, 2, 4))  # Extra margin for second y-axis

    if (sum(is_hub) > 0) {
      hub_families <- V(network)$name[is_hub]
      hub_communities <- membership(cross_network_result$communities)[is_hub]
      hub_degrees <- cross_network_result$node_degrees[is_hub]
      hub_betweenness <- cross_network_result$betweenness[is_hub]
      hub_types <- V(network)$type[is_hub]

      # Create a summary table
      hub_data <- data.frame(
        Family = hub_families,
        Community = hub_communities,
        Degree = hub_degrees,
        Betweenness = hub_betweenness,
        Type = hub_types,
        stringsAsFactors = FALSE
      )

      # Sort by degree (descending)
      hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

      # Show top families (limit to avoid overcrowding)
      if (nrow(hub_data) <= 10) {
        families_to_show <- hub_data
      } else {
        families_to_show <- hub_data[1:10, ]
      }

      # Create colors based on group type (bacteria vs fungi)
      type_colors <- ifelse(families_to_show$Type == cross_network_result$group1_name,
                           base_group1_color, base_group2_color)
      type_colors <- adjustcolor(type_colors, alpha.f = 0.8)

      # Create bar plot for degree
      bp <- barplot(families_to_show$Degree,
                    names.arg = families_to_show$Family,
                    col = type_colors,
                    las = 2,
                    cex.names = 0.6,
                    main = "Hub Families: Degree (bars) & Betweenness (line)",
                    ylab = "Degree",
                    cex.main = 0.9,
                    ylim = c(0, max(families_to_show$Degree) * 1.1))

      # Add betweenness as a line plot on secondary y-axis
      par(new = TRUE)

      # Scale betweenness to fit nicely with degree scale
      betweenness_scaled <- families_to_show$Betweenness
      if (max(betweenness_scaled) > 0) {
        betweenness_scaled <- (betweenness_scaled / max(betweenness_scaled)) * max(families_to_show$Degree) * 0.8
      }

      plot(bp, betweenness_scaled,
           type = "o",
           pch = 16,
           col = "red",
           lwd = 2,
           cex = 1.2,
           axes = FALSE,
           xlab = "",
           ylab = "")

      # Add right y-axis for betweenness
      axis(4,
           at = seq(0, max(families_to_show$Degree) * 0.8, length.out = 5),
           labels = round(seq(0, max(families_to_show$Betweenness), length.out = 5), 1),
           col = "red",
           col.axis = "red")
      mtext("Betweenness", side = 4, line = 2.5, col = "red", cex = 0.8)

      # Add legend for both metrics and groups
      legend("topright",
             legend = c("Degree (bars)", "Betweenness (line)",
                       cross_network_result$group1_name, cross_network_result$group2_name),
             fill = c("gray", NA, base_group1_color, base_group2_color),
             border = c("black", NA, "black", "black"),
             lty = c(NA, 1, NA, NA),
             pch = c(NA, 16, NA, NA),
             col = c("black", "red", "black", "black"),
             cex = 0.6,
             bg = "white")

    } else {
      # If no hub nodes, show a message
      plot.new()
      text(0.5, 0.5, "No hub nodes identified\n(top 5% threshold)",
           cex = 1.2, adj = 0.5)
    }

    # Add improved legend with muted colors (only positive correlations)
    legend("bottomright",
           legend = c(paste(group1_name, "nodes"), paste(group2_name, "nodes"),
                     "Positive correlation", "Hub nodes (top 5%)"),
           col = c(base_group1_color, base_group2_color, "#228B22", "black"),
           pch = c(16, 16, NA, 16),
           lty = c(NA, NA, 1, NA),
           cex = 0.9,
           bg = "white",
           box.lty = 1)

    # Reset layout
    layout(1)

    dev.off()

    cat("Cross-network plot saved:", filename, "\n")
    cat("Hub nodes identified:", sum(is_hub), "out of", vcount(network), "total nodes\n")
    cat("Group 1 (", cross_network_result$group1_name, ") nodes:", sum(V(network)$type == cross_network_result$group1_name), "\n")
    cat("Group 2 (", cross_network_result$group2_name, ") nodes:", sum(V(network)$type == cross_network_result$group2_name), "\n")

  }, error = function(e) {
    cat("Error creating cross-network plot:", e$message, "\n")
    cat("Error details:", e$message, "\n")
  })
}

# Function to save hub node analysis
save_hub_node_analysis <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    return(NULL)
  }

  tryCatch({
    # Create hub node data frame
    hub_data <- data.frame(
      Taxon = network_result$hub_nodes,
      Degree = network_result$node_degrees[network_result$hub_nodes],
      Betweenness = network_result$betweenness[network_result$hub_nodes],
      Closeness = network_result$closeness[network_result$hub_nodes],
      Community = membership(network_result$communities)[network_result$hub_nodes],
      stringsAsFactors = FALSE
    )

    # Sort by degree
    hub_data <- hub_data[order(hub_data$Degree, decreasing = TRUE), ]

    # Save to CSV
    filename <- paste0(group_name, "_", taxonomic_level, "_hub_nodes.csv")
    write.csv(hub_data, filename, row.names = FALSE)

    cat("Hub node analysis saved:", filename, "\n")

    return(hub_data)

  }, error = function(e) {
    cat("Error saving hub node analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to save community analysis
save_community_analysis <- function(network_result, group_name, taxonomic_level) {
  if (is.null(network_result)) {
    return(NULL)
  }

  tryCatch({
    # Create community data frame
    community_data <- data.frame(
      Taxon = network_result$taxa_names,
      Community = membership(network_result$communities),
      Degree = network_result$node_degrees,
      Betweenness = network_result$betweenness,
      Closeness = network_result$closeness,
      stringsAsFactors = FALSE
    )

    # Sort by community and degree
    community_data <- community_data[order(community_data$Community, community_data$Degree, decreasing = c(FALSE, TRUE)), ]

    # Save to CSV
    filename <- paste0(group_name, "_", taxonomic_level, "_communities.csv")
    write.csv(community_data, filename, row.names = FALSE)

    cat("Community analysis saved:", filename, "\n")

    return(community_data)

  }, error = function(e) {
    cat("Error saving community analysis:", e$message, "\n")
    return(NULL)
  })
}

# Function to create network comparison table
create_network_comparison_table <- function(network_results_list, taxonomic_level) {
  tryCatch({
    comparison_data <- data.frame(
      Network = character(),
      Nodes = numeric(),
      Edges = numeric(),
      Avg_Degree = numeric(),
      Max_Degree = numeric(),
      Communities = numeric(),
      Modularity = numeric(),
      Hub_Nodes = numeric(),
      Density = numeric(),
      stringsAsFactors = FALSE
    )

    for (name in names(network_results_list)) {
      result <- network_results_list[[name]]
      if (!is.null(result)) {
        comparison_data <- rbind(comparison_data, data.frame(
          Network = name,
          Nodes = vcount(result$network),
          Edges = ecount(result$network),
          Avg_Degree = mean(result$node_degrees),
          Max_Degree = max(result$node_degrees),
          Communities = result$community_count,
          Modularity = result$modularity,
          Hub_Nodes = length(result$hub_nodes),
          Density = edge_density(result$network),
          stringsAsFactors = FALSE
        ))
      }
    }

    # Save comparison table
    filename <- paste0("Network_Comparison_", taxonomic_level, "_Level.csv")
    write.csv(comparison_data, filename, row.names = FALSE)

    cat("Network comparison table saved:", filename, "\n")

    return(comparison_data)

  }, error = function(e) {
    cat("Error creating comparison table:", e$message, "\n")
    return(NULL)
  })
}

# Function to create statistical comparison plots
create_statistical_plots <- function(individual_networks, cross_networks, taxonomic_level) {
  tryCatch({
    # Prepare data for individual networks
    if (length(individual_networks) > 0) {
      individual_stats <- data.frame(
        Network = names(individual_networks),
        Nodes = sapply(individual_networks, function(x) vcount(x$network)),
        Edges = sapply(individual_networks, function(x) ecount(x$network)),
        Avg_Degree = sapply(individual_networks, function(x) mean(x$node_degrees)),
        Max_Degree = sapply(individual_networks, function(x) max(x$node_degrees)),
        Communities = sapply(individual_networks, function(x) x$community_count),
        Modularity = sapply(individual_networks, function(x) x$modularity),
        Density = sapply(individual_networks, function(x) edge_density(x$network)),
        stringsAsFactors = FALSE
      )

      # Add grouping variables
      individual_stats$Kingdom <- ifelse(grepl("Bacteria", individual_stats$Network), "Bacteria", "Fungi")
      individual_stats$Protection <- ifelse(grepl("Low", individual_stats$Network), "Low", "High")

      # Create statistical comparison plots with better formatting
      pdf(paste0("Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 18, height = 14)

      # Set up multi-panel plot with better margins
      par(mfrow = c(3, 3), mar = c(5, 4, 3, 2), oma = c(2, 2, 2, 2))

      # Plot 1: Number of nodes by kingdom and protection
      boxplot(Nodes ~ Kingdom + Protection, data = individual_stats,
              main = "Number of Nodes", xlab = "Kingdom + Protection", ylab = "Nodes",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 2: Number of edges by kingdom and protection
      boxplot(Edges ~ Kingdom + Protection, data = individual_stats,
              main = "Number of Edges", xlab = "Kingdom + Protection", ylab = "Edges",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 3: Average degree by kingdom and protection
      boxplot(Avg_Degree ~ Kingdom + Protection, data = individual_stats,
              main = "Average Degree", xlab = "Kingdom + Protection", ylab = "Average Degree",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 4: Communities by kingdom and protection
      boxplot(Communities ~ Kingdom + Protection, data = individual_stats,
              main = "Number of Communities", xlab = "Kingdom + Protection", ylab = "Communities",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 5: Modularity by kingdom and environment
      boxplot(Modularity ~ Kingdom + Environment, data = individual_stats,
              main = "Modularity", xlab = "Kingdom + Environment", ylab = "Modularity",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 6: Network density by kingdom and environment
      boxplot(Density ~ Kingdom + Environment, data = individual_stats,
              main = "Network Density", xlab = "Kingdom + Environment", ylab = "Density",
              col = adjustcolor(c("#87CEEB", "#98D982", "#87CEEB", "#98D982"), alpha.f = 0.8), las = 2)

      # Plot 7: Nodes comparison Old vs New
      boxplot(Nodes ~ Time, data = individual_stats,
              main = "Nodes: Old vs New", xlab = "Time", ylab = "Nodes",
              col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))

      # Plot 8: Modularity comparison Old vs New
      boxplot(Modularity ~ Time, data = individual_stats,
              main = "Modularity: Old vs New", xlab = "Time", ylab = "Modularity",
              col = adjustcolor(c("#FFB6C1", "#98FB98"), alpha.f = 0.8))

      # Plot 9: Summary barplot
      barplot(individual_stats$Nodes, names.arg = individual_stats$Network,
              main = "Network Sizes Overview", xlab = "Networks", ylab = "Number of Nodes",
              col = adjustcolor(rainbow(nrow(individual_stats)), alpha.f = 0.7),
              las = 2, cex.names = 0.7)

      dev.off()

      cat("Statistical comparison plots saved: Network_Statistics_", taxonomic_level, "_Level.pdf\n")
    }

    # Create cross-network statistics if available
    if (length(cross_networks) > 0) {
      cross_stats <- data.frame(
        Network = names(cross_networks),
        Nodes = sapply(cross_networks, function(x) vcount(x$network)),
        Edges = sapply(cross_networks, function(x) ecount(x$network)),
        Avg_Degree = sapply(cross_networks, function(x) mean(x$node_degrees)),
        Communities = sapply(cross_networks, function(x) x$community_count),
        Modularity = sapply(cross_networks, function(x) x$modularity),
        stringsAsFactors = FALSE
      )

      # Create cross-network comparison plot
      pdf(paste0("Cross_Network_Statistics_", taxonomic_level, "_Level.pdf"), width = 12, height = 8)

      par(mfrow = c(2, 3), mar = c(4, 4, 3, 2))

      barplot(cross_stats$Nodes, names.arg = cross_stats$Network,
              main = "Cross-Network Nodes", ylab = "Nodes",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Edges, names.arg = cross_stats$Network,
              main = "Cross-Network Edges", ylab = "Edges",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Avg_Degree, names.arg = cross_stats$Network,
              main = "Cross-Network Average Degree", ylab = "Average Degree",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Communities, names.arg = cross_stats$Network,
              main = "Cross-Network Communities", ylab = "Communities",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      barplot(cross_stats$Modularity, names.arg = cross_stats$Network,
              main = "Cross-Network Modularity", ylab = "Modularity",
              col = adjustcolor("#DDA0DD", alpha.f = 0.8), las = 2, cex.names = 0.8)

      # Summary plot
      plot(cross_stats$Edges, cross_stats$Modularity,
           pch = 16, col = adjustcolor("#DDA0DD", alpha.f = 0.8), cex = 2,
           main = "Cross-Network: Edges vs Modularity",
           xlab = "Number of Edges", ylab = "Modularity")
      text(cross_stats$Edges, cross_stats$Modularity,
           labels = cross_stats$Network, pos = 3, cex = 0.7)

      dev.off()

      cat("Cross-network statistics saved: Cross_Network_Statistics_", taxonomic_level, "_Level.pdf\n")
    }

  }, error = function(e) {
    cat("Error creating statistical plots:", e$message, "\n")
  })
}

# Generate all plots and outputs
cat("\n\n========== GENERATING PLOTS AND OUTPUTS ==========\n")

# Plot individual networks and save analyses
individual_networks <- list()

if (exists("bacteria_low_network") && !is.null(bacteria_low_network)) {
  plot_individual_network(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_low_network, "Bacteria_Low_Protection", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_Low_Protection"]] <- bacteria_low_network
}

if (exists("bacteria_high_network") && !is.null(bacteria_high_network)) {
  plot_individual_network(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
  save_community_analysis(bacteria_high_network, "Bacteria_High_Protection", TAXONOMIC_LEVEL)
  individual_networks[["Bacteria_High_Protection"]] <- bacteria_high_network
}

if (exists("fungi_low_network") && !is.null(fungi_low_network)) {
  plot_individual_network(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_low_network, "Fungi_Low_Protection", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_Low_Protection"]] <- fungi_low_network
}

if (exists("fungi_high_network") && !is.null(fungi_high_network)) {
  plot_individual_network(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
  save_community_analysis(fungi_high_network, "Fungi_High_Protection", TAXONOMIC_LEVEL)
  individual_networks[["Fungi_High_Protection"]] <- fungi_high_network
}

# Plot cross-correlation networks
cross_networks <- list()

if (exists("bacteria_fungi_low_network") && !is.null(bacteria_fungi_low_network)) {
  plot_cross_network(bacteria_fungi_low_network, "Bacteria_Low_Protection", "Fungi_Low_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_low_network, "Bacteria_Fungi_Low_Protection", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_Low_Protection"]] <- bacteria_fungi_low_network
}

if (exists("bacteria_fungi_high_network") && !is.null(bacteria_fungi_high_network)) {
  plot_cross_network(bacteria_fungi_high_network, "Bacteria_High_Protection", "Fungi_High_Protection", TAXONOMIC_LEVEL)
  save_hub_node_analysis(bacteria_fungi_high_network, "Bacteria_Fungi_High_Protection", TAXONOMIC_LEVEL)
  cross_networks[["Bacteria_Fungi_High_Protection"]] <- bacteria_fungi_high_network
}

# Create comparison tables
if (length(individual_networks) > 0) {
  individual_comparison <- create_network_comparison_table(individual_networks, paste0(TAXONOMIC_LEVEL, "_Individual"))
}

if (length(cross_networks) > 0) {
  cross_comparison <- create_network_comparison_table(cross_networks, paste0(TAXONOMIC_LEVEL, "_Cross"))
}

# Create statistical plots
cat("\n========== CREATING STATISTICAL PLOTS ==========\n")
create_statistical_plots(individual_networks, cross_networks, TAXONOMIC_LEVEL)

cat("\n\n========== ANALYSIS COMPLETED ==========\n")
cat("Taxonomic level:", TAXONOMIC_LEVEL, "\n")
cat("Individual networks analyzed:", length(individual_networks), "\n")
cat("Cross-correlation networks analyzed:", length(cross_networks), "\n")
cat("All plots, hub node analyses, community analyses, and comparison tables generated!\n")

# Print summary of generated files
cat("\n========== GENERATED FILES SUMMARY ==========\n")
cat("PDF Network Plots:\n")
for (name in names(individual_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_network.pdf"), "\n")
}
for (name in names(cross_networks)) {
  # Extract group names for cross-network filename
  if (name == "Bacteria_Fungi_Low_Protection") {
    cat("  - Bacteria_Low_Protection_vs_Fungi_Low_Protection_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  } else if (name == "Bacteria_Fungi_High_Protection") {
    cat("  - Bacteria_High_Protection_vs_Fungi_High_Protection_", TAXONOMIC_LEVEL, "_cross_network.pdf\n")
  }
}

cat("\nCSV Analysis Files:\n")
for (name in names(individual_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_hub_nodes.csv"), "\n")
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_communities.csv"), "\n")
}
for (name in names(cross_networks)) {
  cat("  -", paste0(name, "_", TAXONOMIC_LEVEL, "_hub_nodes.csv"), "\n")
}

cat("\nComparison Tables:\n")
if (length(individual_networks) > 0) {
  cat("  - Network_Comparison_", TAXONOMIC_LEVEL, "_Individual_Level.csv\n")
}
if (length(cross_networks) > 0) {
  cat("  - Network_Comparison_", TAXONOMIC_LEVEL, "_Cross_Level.csv\n")
}

cat("\n🎉 Complete taxonomic network analysis finished successfully! 🎉\n")

