# Simplified <PERSON><PERSON><PERSON> and PERMANOVA Analysis
# This version handles the p-value extraction issue more robustly

library(readxl)
library(vegan)

cat("========== LOADING DATA ==========\n")

# Read data files
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
fungi_otu <- read_excel("OTU_table_fungi.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

cat("Data loaded successfully!\n")

# Function to prepare data
prepare_data <- function(otu_table, metadata) {
  abundance_matrix <- as.matrix(otu_table[, -1])
  rownames(abundance_matrix) <- otu_table[[1]]
  abundance_matrix <- t(abundance_matrix)
  
  common_samples <- intersect(rownames(abundance_matrix), metadata$Sample)
  abundance_matrix <- abundance_matrix[common_samples, ]
  metadata_filtered <- metadata[metadata$Sample %in% common_samples, ]
  metadata_filtered <- metadata_filtered[match(rownames(abundance_matrix), metadata_filtered$Sample), ]
  
  # Remove zero-abundance OTUs
  abundance_matrix <- abundance_matrix[, colSums(abundance_matrix) > 0]
  
  return(list(abundance = abundance_matrix, metadata = metadata_filtered))
}

# Prepare data
bacteria_data <- prepare_data(bacteria_otu, bacteria_meta)
fungi_data <- prepare_data(fungi_otu, fungi_meta)

# Create distance matrices
bacteria_dist <- vegdist(bacteria_data$abundance, method = "bray")
fungi_dist <- vegdist(fungi_data$abundance, method = "bray")

cat("Data prepared successfully!\n")

# Function to perform analysis safely
perform_analysis <- function(dist_matrix, metadata, organism_name) {
  cat("\n========== ANALYSIS FOR", organism_name, "==========\n")
  
  results <- list()
  
  # PERMANOVA tests
  cat("\n--- PERMANOVA Results ---\n")
  
  # Season effect
  perm_season <- adonis2(dist_matrix ~ Season, data = metadata, permutations = 999)
  cat("Season PERMANOVA:\n")
  print(perm_season)
  results$permanova_season <- perm_season
  
  # Protection effect
  perm_protection <- adonis2(dist_matrix ~ Protection, data = metadata, permutations = 999)
  cat("\nProtection PERMANOVA:\n")
  print(perm_protection)
  results$permanova_protection <- perm_protection
  
  # Combined model
  perm_combined <- adonis2(dist_matrix ~ Season + Protection + Season:Protection, data = metadata, permutations = 999)
  cat("\nCombined model PERMANOVA:\n")
  print(perm_combined)
  results$permanova_combined <- perm_combined
  
  # Betadisper tests
  cat("\n--- Betadisper Results ---\n")
  
  # Season betadisper
  betadisp_season <- betadisper(dist_matrix, metadata$Season)
  betadisp_test_season <- permutest(betadisp_season, permutations = 999)
  cat("Season Betadisper F-statistic:", betadisp_test_season$statistic, "\n")

  # Extract p-value correctly from tab component
  season_p <- tryCatch({
    if (!is.null(betadisp_test_season$tab) && "Pr(>F)" %in% colnames(betadisp_test_season$tab)) {
      betadisp_test_season$tab[1, "Pr(>F)"]
    } else {
      # Alternative: use anova on betadisper object
      anova_result <- anova(betadisp_season)
      anova_result[1, "Pr(>F)"]
    }
  }, error = function(e) NA)

  cat("Season Betadisper p-value:", season_p, "\n")
  if (!is.na(season_p)) {
    if (season_p < 0.05) {
      cat("*** Season dispersions significantly different (p < 0.05) ***\n")
    } else {
      cat("Season dispersions not significantly different (p >= 0.05)\n")
    }
  }
  results$betadisp_season <- list(result = betadisp_season, test = betadisp_test_season, p_value = season_p)

  # Protection betadisper
  betadisp_protection <- betadisper(dist_matrix, metadata$Protection)
  betadisp_test_protection <- permutest(betadisp_protection, permutations = 999)
  cat("Protection Betadisper F-statistic:", betadisp_test_protection$statistic, "\n")

  # Extract p-value correctly from tab component
  protection_p <- tryCatch({
    if (!is.null(betadisp_test_protection$tab) && "Pr(>F)" %in% colnames(betadisp_test_protection$tab)) {
      betadisp_test_protection$tab[1, "Pr(>F)"]
    } else {
      # Alternative: use anova on betadisper object
      anova_result <- anova(betadisp_protection)
      anova_result[1, "Pr(>F)"]
    }
  }, error = function(e) NA)

  cat("Protection Betadisper p-value:", protection_p, "\n")
  if (!is.na(protection_p)) {
    if (protection_p < 0.05) {
      cat("*** Protection dispersions significantly different (p < 0.05) ***\n")
    } else {
      cat("Protection dispersions not significantly different (p >= 0.05)\n")
    }
  }
  results$betadisp_protection <- list(result = betadisp_protection, test = betadisp_test_protection, p_value = protection_p)
  
  return(results)
}

# Perform analyses
bacteria_results <- perform_analysis(bacteria_dist, bacteria_data$metadata, "BACTERIA")
fungi_results <- perform_analysis(fungi_dist, fungi_data$metadata, "FUNGI")

# Season-specific analyses
cat("\n========== SEASON-SPECIFIC PROTECTION ANALYSES ==========\n")

seasons <- unique(bacteria_data$metadata$Season)

for (season in seasons) {
  cat("\n--- Analysis for", season, "Season ---\n")
  
  # Bacteria season-specific
  bact_season_idx <- bacteria_data$metadata$Season == season
  bact_season_meta <- bacteria_data$metadata[bact_season_idx, ]
  bact_season_abund <- bacteria_data$abundance[bact_season_idx, ]
  bact_season_abund <- bact_season_abund[, colSums(bact_season_abund) > 0]
  bact_season_dist <- vegdist(bact_season_abund, method = "bray")
  
  cat("BACTERIA", season, "- Protection PERMANOVA:\n")
  bact_season_perm <- adonis2(bact_season_dist ~ Protection, data = bact_season_meta, permutations = 999)
  print(bact_season_perm)
  
  cat("BACTERIA", season, "- Protection Betadisper:\n")
  bact_season_betadisp <- betadisper(bact_season_dist, bact_season_meta$Protection)
  bact_season_betadisp_test <- permutest(bact_season_betadisp, permutations = 999)

  # Extract p-value correctly
  bact_p <- tryCatch({
    if (!is.null(bact_season_betadisp_test$tab) && "Pr(>F)" %in% colnames(bact_season_betadisp_test$tab)) {
      bact_season_betadisp_test$tab[1, "Pr(>F)"]
    } else {
      anova_result <- anova(bact_season_betadisp)
      anova_result[1, "Pr(>F)"]
    }
  }, error = function(e) NA)

  cat("F-statistic:", bact_season_betadisp_test$statistic, "p-value:", bact_p, "\n")
  if (!is.na(bact_p)) {
    if (bact_p < 0.05) cat("*** Significant dispersion difference ***\n")
  }
  
  # Fungi season-specific
  fungi_season_idx <- fungi_data$metadata$Season == season
  fungi_season_meta <- fungi_data$metadata[fungi_season_idx, ]
  fungi_season_abund <- fungi_data$abundance[fungi_season_idx, ]
  fungi_season_abund <- fungi_season_abund[, colSums(fungi_season_abund) > 0]
  fungi_season_dist <- vegdist(fungi_season_abund, method = "bray")
  
  cat("FUNGI", season, "- Protection PERMANOVA:\n")
  fungi_season_perm <- adonis2(fungi_season_dist ~ Protection, data = fungi_season_meta, permutations = 999)
  print(fungi_season_perm)
  
  cat("FUNGI", season, "- Protection Betadisper:\n")
  fungi_season_betadisp <- betadisper(fungi_season_dist, fungi_season_meta$Protection)
  fungi_season_betadisp_test <- permutest(fungi_season_betadisp, permutations = 999)

  # Extract p-value correctly
  fungi_p <- tryCatch({
    if (!is.null(fungi_season_betadisp_test$tab) && "Pr(>F)" %in% colnames(fungi_season_betadisp_test$tab)) {
      fungi_season_betadisp_test$tab[1, "Pr(>F)"]
    } else {
      anova_result <- anova(fungi_season_betadisp)
      anova_result[1, "Pr(>F)"]
    }
  }, error = function(e) NA)

  cat("F-statistic:", fungi_season_betadisp_test$statistic, "p-value:", fungi_p, "\n")
  if (!is.na(fungi_p)) {
    if (fungi_p < 0.05) cat("*** Significant dispersion difference ***\n")
  }
}

cat("\n========== CREATING SUMMARY TABLE ==========\n")

# Create summary table
summary_table <- data.frame(
  Analysis = character(),
  Organism = character(),
  Factor = character(),
  PERMANOVA_F = numeric(),
  PERMANOVA_R2 = numeric(),
  PERMANOVA_p = numeric(),
  PERMANOVA_Significant = logical(),
  Betadisper_F = numeric(),
  Betadisper_p = numeric(),
  Betadisper_Significant = logical(),
  stringsAsFactors = FALSE
)

# Add bacteria results
summary_table <- rbind(summary_table, data.frame(
  Analysis = "Overall",
  Organism = "Bacteria",
  Factor = "Season",
  PERMANOVA_F = bacteria_results$permanova_season$F[1],
  PERMANOVA_R2 = bacteria_results$permanova_season$R2[1],
  PERMANOVA_p = bacteria_results$permanova_season$`Pr(>F)`[1],
  PERMANOVA_Significant = bacteria_results$permanova_season$`Pr(>F)`[1] < 0.05,
  Betadisper_F = bacteria_results$betadisp_season$test$statistic,
  Betadisper_p = bacteria_results$betadisp_season$p_value,
  Betadisper_Significant = !is.na(bacteria_results$betadisp_season$p_value) && bacteria_results$betadisp_season$p_value < 0.05
))

summary_table <- rbind(summary_table, data.frame(
  Analysis = "Overall",
  Organism = "Bacteria",
  Factor = "Protection",
  PERMANOVA_F = bacteria_results$permanova_protection$F[1],
  PERMANOVA_R2 = bacteria_results$permanova_protection$R2[1],
  PERMANOVA_p = bacteria_results$permanova_protection$`Pr(>F)`[1],
  PERMANOVA_Significant = bacteria_results$permanova_protection$`Pr(>F)`[1] < 0.05,
  Betadisper_F = bacteria_results$betadisp_protection$test$statistic,
  Betadisper_p = bacteria_results$betadisp_protection$p_value,
  Betadisper_Significant = !is.na(bacteria_results$betadisp_protection$p_value) && bacteria_results$betadisp_protection$p_value < 0.05
))

# Add fungi results
summary_table <- rbind(summary_table, data.frame(
  Analysis = "Overall",
  Organism = "Fungi",
  Factor = "Season",
  PERMANOVA_F = fungi_results$permanova_season$F[1],
  PERMANOVA_R2 = fungi_results$permanova_season$R2[1],
  PERMANOVA_p = fungi_results$permanova_season$`Pr(>F)`[1],
  PERMANOVA_Significant = fungi_results$permanova_season$`Pr(>F)`[1] < 0.05,
  Betadisper_F = fungi_results$betadisp_season$test$statistic,
  Betadisper_p = fungi_results$betadisp_season$p_value,
  Betadisper_Significant = !is.na(fungi_results$betadisp_season$p_value) && fungi_results$betadisp_season$p_value < 0.05
))

summary_table <- rbind(summary_table, data.frame(
  Analysis = "Overall",
  Organism = "Fungi",
  Factor = "Protection",
  PERMANOVA_F = fungi_results$permanova_protection$F[1],
  PERMANOVA_R2 = fungi_results$permanova_protection$R2[1],
  PERMANOVA_p = fungi_results$permanova_protection$`Pr(>F)`[1],
  PERMANOVA_Significant = fungi_results$permanova_protection$`Pr(>F)`[1] < 0.05,
  Betadisper_F = fungi_results$betadisp_protection$test$statistic,
  Betadisper_p = fungi_results$betadisp_protection$p_value,
  Betadisper_Significant = !is.na(fungi_results$betadisp_protection$p_value) && fungi_results$betadisp_protection$p_value < 0.05
))

# Save summary table
write.csv(summary_table, "statistical_analysis_summary.csv", row.names = FALSE)

cat("Summary table saved to: statistical_analysis_summary.csv\n")
cat("\n========== SUMMARY TABLE ==========\n")
print(summary_table)

cat("\n========== ANALYSIS COMPLETE ==========\n")
cat("Key interpretations:\n")
cat("- PERMANOVA p < 0.05: Significant difference in community composition\n")
cat("- Betadisper p < 0.05: Significant difference in dispersions (variance)\n")
cat("- If betadisper is significant, interpret PERMANOVA with caution\n")
cat("- R2 values show proportion of variance explained by each factor\n")
