# Test script to verify data loading and basic analysis
library(readxl)
library(vegan)

cat("========== TESTING DATA LOADING ==========\n")

# Read data files
bacteria_otu <- read_excel("OTU_table_bacteria.xlsx")
fungi_otu <- read_excel("OTU_table_fungi.xlsx")
bacteria_meta <- read_excel("metadata_bacteria.xlsx")
fungi_meta <- read_excel("metadata_fungi.xlsx")

cat("Data loaded successfully!\n")
cat("Bacteria OTU dimensions:", dim(bacteria_otu), "\n")
cat("Fungi OTU dimensions:", dim(fungi_otu), "\n")
cat("Bacteria metadata dimensions:", dim(bacteria_meta), "\n")
cat("Fungi metadata dimensions:", dim(fungi_meta), "\n")

# Check metadata structure
cat("\nMetadata structure:\n")
cat("Bacteria metadata columns:", colnames(bacteria_meta), "\n")
cat("Season levels:", unique(bacteria_meta$Season), "\n")
cat("Protection levels:", unique(bacteria_meta$Protection), "\n")

# Test basic data preparation
cat("\n========== TESTING DATA PREPARATION ==========\n")

# Prepare bacteria data for analysis
abundance_matrix <- as.matrix(bacteria_otu[, -1])
rownames(abundance_matrix) <- bacteria_otu[[1]]
abundance_matrix <- t(abundance_matrix)

cat("Abundance matrix dimensions:", dim(abundance_matrix), "\n")
cat("Sample names (first 10):", head(rownames(abundance_matrix), 10), "\n")
cat("Metadata sample names (first 10):", head(bacteria_meta$Sample, 10), "\n")

# Check for common samples
common_samples <- intersect(rownames(abundance_matrix), bacteria_meta$Sample)
cat("Number of common samples:", length(common_samples), "\n")

if (length(common_samples) > 0) {
  # Filter to common samples
  abundance_matrix <- abundance_matrix[common_samples, ]
  metadata_filtered <- bacteria_meta[bacteria_meta$Sample %in% common_samples, ]
  metadata_filtered <- metadata_filtered[match(rownames(abundance_matrix), metadata_filtered$Sample), ]
  
  cat("Filtered data dimensions:", dim(abundance_matrix), "\n")
  cat("Filtered metadata dimensions:", dim(metadata_filtered), "\n")
  
  # Test distance matrix creation
  cat("\n========== TESTING DISTANCE MATRIX ==========\n")
  
  # Remove OTUs with zero abundance
  abundance_matrix <- abundance_matrix[, colSums(abundance_matrix) > 0]
  cat("OTUs after filtering zeros:", ncol(abundance_matrix), "\n")
  
  # Create Bray-Curtis distance matrix
  dist_matrix <- vegdist(abundance_matrix, method = "bray")
  cat("Distance matrix created successfully\n")
  cat("Distance matrix size:", length(dist_matrix), "pairwise distances\n")
  
  # Test basic PERMANOVA
  cat("\n========== TESTING PERMANOVA ==========\n")
  
  # Simple PERMANOVA for Season
  permanova_season <- adonis2(dist_matrix ~ Season, data = metadata_filtered, permutations = 99)
  cat("PERMANOVA for Season:\n")
  print(permanova_season)
  
  # Simple PERMANOVA for Protection
  permanova_protection <- adonis2(dist_matrix ~ Protection, data = metadata_filtered, permutations = 99)
  cat("\nPERMANOVA for Protection:\n")
  print(permanova_protection)
  
  # Test betadisper
  cat("\n========== TESTING BETADISPER ==========\n")
  
  # Betadisper for Season
  betadisp_season <- betadisper(dist_matrix, metadata_filtered$Season)
  betadisp_test_season <- permutest(betadisp_season, permutations = 99)
  cat("Betadisper for Season - p-value:", betadisp_test_season$p.value, "\n")
  
  # Betadisper for Protection
  betadisp_protection <- betadisper(dist_matrix, metadata_filtered$Protection)
  betadisp_test_protection <- permutest(betadisp_protection, permutations = 99)
  cat("Betadisper for Protection - p-value:", betadisp_test_protection$p.value, "\n")
  
  cat("\n========== TEST COMPLETED SUCCESSFULLY ==========\n")
  
} else {
  cat("ERROR: No common samples found between OTU table and metadata\n")
}
