Warning message:
package 'readxl' was built under R version 4.4.3 
Loading required package: permute
Warning message:
package 'vegan' was built under R version 4.4.3 
── Attaching core tidyverse packages ──────────────────────── tidyverse 2.0.0 ──
✔ dplyr     1.1.4     ✔ readr     2.1.5
✔ forcats   1.0.0     ✔ stringr   1.5.1
✔ ggplot2   3.5.2     ✔ tibble    3.3.0
✔ lubridate 1.9.4     ✔ tidyr     1.3.1
✔ purrr     1.0.4     
── Conflicts ────────────────────────────────────────── tidyverse_conflicts() ──
✖ dplyr::filter() masks stats::filter()
✖ dplyr::lag()    masks stats::lag()
ℹ Use the conflicted package (<http://conflicted.r-lib.org/>) to force all conflicts to become errors
Warning messages:
1: package 'ggplot2' was built under R version 4.4.3 
2: package 'tibble' was built under R version 4.4.3 
3: package 'purrr' was built under R version 4.4.3 
4: package 'lubridate' was built under R version 4.4.3 

Attaching package: 'gridExtra'

The following object is masked from 'package:dplyr':

    combine

========== LOADING DATA FILES ==========
Data dimensions:
Bacteria OTU: 53189 151 
Fungi OTU: 15894 151 
Bacteria metadata: 150 3 
Fungi metadata: 150 3 

Factor levels:
Season levels: Early Peak Late 
Protection levels: Low High 

========== PREPARING DATA ==========
Prepared data with 150 samples and 53189 OTUs
Creating bray distance matrix...
Using 53189 OTUs after filtering zeros
Prepared data with 150 samples and 15894 OTUs
Creating bray distance matrix...
Using 15892 OTUs after filtering zeros

========== BACTERIA COMMUNITY ANALYSIS ==========

=== BACTERIA: Overall Season and Protection Analysis ===

--- Betadisper Analysis for Season ---
Betadisper test results:
F-statistic: 3.577087 1.77325 -0.9655752 -2.728894 
p-value: 
Error in if (betadisp_test$p.value < 0.05) { : argument is of length zero
Calls: perform_betadisper
Execution halted
